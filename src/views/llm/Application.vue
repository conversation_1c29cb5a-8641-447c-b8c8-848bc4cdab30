<script setup>
import { computed, getCurrentInstance, onMounted, ref } from 'vue'
import ApplicationApi from '@/api/llm/application.js'
import PluginApi from '@/api/llm/plugin.js'

const { proxy } = getCurrentInstance()

const baseURL = import.meta.env.VITE_DIFY_BASE_URL

const signInURL = computed(() => {
  return `${baseURL}/signin`
})

const applicationId = ref('')

const route = computed(() => `${baseURL}/explore/installed/${applicationId.value}`)

const dify = ref()

const load = () => {
  const _window = dify.value.contentWindow
  const _document = _window.document

  // 注入样式，隐藏元素
  const _style = _document.createElement('style')
  _style.textContent = `
        /* 隐藏顶部栏 */
        body > div > div > div.basis-auto {
          display: none;
        }

        /* 隐藏侧边栏 */
        body > div > div > div.bg-background-body > div.border-divider-burn {
          display: none;
        }

        /* 隐藏对话列表 */
        body > div > div > div.bg-background-body > div.grow > div > div > div.ease-in-out > div > div.grow > div {
          display: none;
        }
      `
  _document.head.appendChild(_style)

  // 注入脚本，监听路由变化
  const _script = `
        (function() {
          const pushState = window.history.pushState;
          const replaceState = window.history.replaceState;

          window.history.pushState = function(...args) {
            pushState.apply(window.history, args);
            window.parent.postMessage({
              type: 'dify-route-change',
              url: window.location.href
            }, "*");
          };

          window.history.replaceState = function(...args) {
            replaceState.apply(window.history, args);
            window.parent.postMessage({
              type: "dify-route-change",
              url: window.location.href
            }, "*");
          };

          window.addEventListener("popstate", () => {
            window.parent.postMessage({
              type: "dify-route-change",
              url: window.location.href
            }, "*");
          });
        })();
      `
  _window.eval(_script)

  window.addEventListener('message', event => {
    if (event.data.type === 'dify-route-change') {
      // 登录页面
      if (signInURL.value === event.data.url) {
        PluginApi.difyToken({
          toast: {
            success: false
          }
        })
          .then(token => {
            // 缓存
            _window.localStorage.setItem('console_token', token.data.accessToken)
            _window.localStorage.setItem('refresh_token', token.data.refreshToken)

            // 重定向
            _window.location.href = route.value
          })
      }
    }
  })
}

onMounted(() => {
  applicationId.value = proxy.$route.query.id
  if (applicationId.value == null) {
    proxy.$router.replace({
      name: 'llm.application.index'
    })
  }
})
</script>

<template>
  <template v-if="applicationId">
    <iframe
      ref="dify"
      :src="route"
      @load="load"
    />
  </template>
</template>

<style lang="less" scoped>
iframe {
  border: none;
  height: calc(100vh - 64px - 64px - 12px);
  width: 100%;
}
</style>
